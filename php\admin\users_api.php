<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../dbconnection.php';

class UsersAPI {
    private $conn;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                case 'PUT':
                    $this->handlePut($action);
                    break;
                case 'DELETE':
                    $this->handleDelete($action);
                    break;
                default:
                    $this->sendResponse(['error' => 'Method not allowed'], 405);
            }
        } catch (Exception $e) {
            $this->sendResponse(['error' => $e->getMessage()], 500);
        }
    }
    
    private function handleGet($action) {
        switch ($action) {
            case 'get_all_users':
                $this->getAllUsers();
                break;
            case 'get_user':
                $userId = $_GET['user_id'] ?? 0;
                $this->getUserDetails($userId);
                break;
            case 'search_users':
                $searchTerm = $_GET['search'] ?? '';
                $this->searchUsers($searchTerm);
                break;
            case 'get_exp_levels':
                $this->getExpLevels();
                break;
            case 'get_users_by_approval_status':
                $approvalStatus = $_GET['approval_status'] ?? '';
                $this->getUsersByApprovalStatus($approvalStatus);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }

    private function handlePost($action) {
        switch ($action) {
            case 'deactivate_user':
                $userId = $_GET['user_id'] ?? 0;
                $this->deactivateUser($userId);
                break;
            case 'activate_user':
                $userId = $_GET['user_id'] ?? 0;
                $this->activateUser($userId);
                break;
            case 'bulk_approve':
                $this->bulkApprove();
                break;
            case 'bulk_disapprove':
                $this->bulkDisapprove();
                break;
            case 'bulk_activate':
                $this->bulkActivate();
                break;
            case 'bulk_deactivate':
                $this->bulkDeactivate();
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }

    private function handlePut($action) {
        switch ($action) {
            case 'approve_user':
                $this->approveUser();
                break;
            case 'reject_user':
                $this->rejectUser();
                break;
            case 'edit_user':
                $this->editUser();
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }

    private function handleDelete($action) {
        switch ($action) {
            case 'delete_user':
                $userId = $_GET['user_id'] ?? 0;
                $this->deleteUser($userId);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function getAllUsers() {
        $query = "SELECT
                    ua.user_id,
                    ua.username,
                    ua.email,
                    ua.name,
                    ua.bio,
                    ua.avatar,
                    ua.created_at,
                    ua.is_banned,
                    ua.is_approved,
                    ua.semester,
                    ua.schoolyr,
                    COALESCE(ue.userExp, 0) as userExp,
                    COUNT(DISTINCT CASE WHEN ul.isUnlocked = 1 THEN ul.level_number END) as levels_completed,
                    (SELECT COUNT(DISTINCT level_number) FROM game_content) as total_levels
                  FROM user_account ua
                  LEFT JOIN user_exp ue ON ua.user_id = ue.user_id
                  LEFT JOIN user_levels ul ON ua.user_id = ul.user_id
                  GROUP BY ua.user_id, ua.username, ua.email, ua.name, ua.bio, ua.avatar, ua.created_at, ua.is_banned, ua.is_approved, ua.semester, ua.schoolyr, ue.userExp
                  ORDER BY ua.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process users data
        foreach ($users as &$user) {
            $user['expLevel'] = $this->getExpLevel($user['userExp']);
            $user['achievements'] = $this->getUserAchievements($user['user_id']);
            $user['totalAchievements'] = $this->getTotalAchievements();
        }

        $this->sendResponse(['success' => true, 'data' => $users]);
    }

    private function getUsersByApprovalStatus($approvalStatus) {
        // Validate approval status
        $validStatuses = ['pending', 'approved', 'rejected'];
        if (!in_array($approvalStatus, $validStatuses)) {
            $this->sendResponse(['error' => 'Invalid approval status. Must be: pending, approved, or rejected'], 400);
            return;
        }

        // Map status to database values
        $statusValue = match($approvalStatus) {
            'pending' => 0,
            'approved' => 1,
            'rejected' => -1
        };

        $query = "SELECT
                    ua.user_id,
                    ua.username,
                    ua.email,
                    ua.name,
                    ua.bio,
                    ua.avatar,
                    ua.created_at,
                    ua.is_banned,
                    ua.is_approved,
                    ua.semester,
                    ua.schoolyr,
                    COALESCE(ue.userExp, 0) as userExp,
                    COUNT(DISTINCT CASE WHEN ul.isUnlocked = 1 THEN ul.level_number END) as levels_completed,
                    (SELECT COUNT(DISTINCT level_number) FROM game_content) as total_levels
                  FROM user_account ua
                  LEFT JOIN user_exp ue ON ua.user_id = ue.user_id
                  LEFT JOIN user_levels ul ON ua.user_id = ul.user_id
                  WHERE ua.is_approved = ?
                  GROUP BY ua.user_id, ua.username, ua.email, ua.name, ua.bio, ua.avatar, ua.created_at, ua.is_banned, ua.is_approved, ua.semester, ua.schoolyr, ue.userExp
                  ORDER BY ua.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute([$statusValue]);

        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process users data
        foreach ($users as &$user) {
            $user['expLevel'] = $this->getExpLevel($user['userExp']);
            $user['achievements'] = $this->getUserAchievements($user['user_id']);
            $user['totalAchievements'] = $this->getTotalAchievements();
        }

        $this->sendResponse(['success' => true, 'data' => $users]);
    }

    private function getUserDetails($userId) {
        $query = "SELECT
                    ua.user_id,
                    ua.username,
                    ua.email,
                    ua.name,
                    ua.bio,
                    ua.avatar,
                    ua.created_at,
                    ua.is_banned,
                    ua.is_approved,
                    ua.semester,
                    ua.schoolyr,
                    COALESCE(ue.userExp, 0) as userExp
                  FROM user_account ua
                  LEFT JOIN user_exp ue ON ua.user_id = ue.user_id
                  WHERE ua.user_id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);

        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            $user['expLevel'] = $this->getExpLevel($user['userExp']);
            $user['achievements'] = $this->getUserAchievements($userId);
            $user['totalAchievements'] = $this->getTotalAchievements();
            $user['levels_completed'] = $this->getUserLevelsCompleted($userId);
            $user['total_levels'] = $this->getTotalLevels();

            $this->sendResponse(['success' => true, 'data' => $user]);
        } else {
            $this->sendResponse(['success' => false, 'error' => 'User not found'], 404);
        }
    }
    
    private function searchUsers($searchTerm) {
        $query = "SELECT
                    ua.user_id,
                    ua.username,
                    ua.email,
                    ua.name,
                    ua.bio,
                    ua.avatar,
                    ua.created_at,
                    ua.is_banned,
                    ua.is_approved,
                    ua.semester,
                    ua.schoolyr,
                    COALESCE(ue.userExp, 0) as userExp
                  FROM user_account ua
                  LEFT JOIN user_exp ue ON ua.user_id = ue.user_id
                  WHERE ua.username LIKE ? OR ua.email LIKE ? OR ua.name LIKE ?
                  ORDER BY ua.created_at DESC";

        $searchPattern = "%{$searchTerm}%";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$searchPattern, $searchPattern, $searchPattern]);

        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process users data
        foreach ($users as &$user) {
            $user['expLevel'] = $this->getExpLevel($user['userExp']);
            $user['achievements'] = $this->getUserAchievements($user['user_id']);
            $user['totalAchievements'] = $this->getTotalAchievements();
            $user['levels_completed'] = $this->getUserLevelsCompleted($user['user_id']);
            $user['total_levels'] = $this->getTotalLevels();
        }

        $this->sendResponse(['success' => true, 'data' => $users]);
    }
    
    private function deleteUser($userId) {
        try {
            $this->conn->beginTransaction();

            // Delete from user_achievements
            $query = "DELETE FROM user_achievements WHERE user_id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$userId]);

            // Delete from user_exp
            $query = "DELETE FROM user_exp WHERE user_id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$userId]);

            // Delete from user_levels
            $query = "DELETE FROM user_levels WHERE user_id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$userId]);

            // Delete from user_account
            $query = "DELETE FROM user_account WHERE user_id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$userId]);

            $this->conn->commit();
            $this->sendResponse(['success' => true, 'message' => 'User deleted successfully']);
        } catch (Exception $e) {
            $this->conn->rollBack();
            $this->sendResponse(['success' => false, 'error' => 'Failed to delete user: ' . $e->getMessage()], 500);
        }
    }
    
    private function getExpLevels() {
        $query = "SELECT expID, expName, expNeeded FROM exp ORDER BY expNeeded ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        $levels = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($levels) {
            $this->sendResponse(['success' => true, 'data' => $levels]);
        } else {
            $this->sendResponse(['success' => false, 'error' => 'No experience levels found'], 404);
        }
    }

    private function getUserAchievements($userId) {
        $query = "SELECT COUNT(*) as unlocked_count
                  FROM user_achievements ua
                  INNER JOIN achievements a ON ua.achievement_id = a.id
                  WHERE ua.user_id = ? AND ua.unlocked = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['unlocked_count'] ?? 0;
    }

    private function getTotalAchievements() {
        $query = "SELECT COUNT(*) as total FROM achievements";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 50;
    }

    private function getUserLevelsCompleted($userId) {
        $query = "SELECT COUNT(*) as completed FROM user_levels WHERE user_id = ? AND isUnlocked = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['completed'] ?? 0;
    }

    private function getTotalLevels() {
        $query = "SELECT COUNT(DISTINCT level_number) as total FROM game_content";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 6;
    }

    private function getExpLevel($userExp) {
        $query = "SELECT expName FROM exp WHERE expNeeded <= ? ORDER BY expNeeded DESC LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userExp]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['expName'] ?? '1';
    }

    private function deactivateUser($userId) {
        if (!$userId) {
            $this->sendResponse(['success' => false, 'error' => 'User ID required'], 400);
        }
        $query = "UPDATE user_account SET is_banned = 1 WHERE user_id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);
        $this->getUserDetails($userId);
    }

    private function activateUser($userId) {
        if (!$userId) {
            $this->sendResponse(['success' => false, 'error' => 'User ID required'], 400);
        }
        $query = "UPDATE user_account SET is_banned = 0 WHERE user_id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);
        $this->getUserDetails($userId);
    }

    private function approveUser() {
        $input = json_decode(file_get_contents('php://input'), true);
        $userId = $input['user_id'] ?? 0;
        $semester = $input['semester'] ?? null;
        $schoolyr = $input['schoolyr'] ?? null;

        if (!$userId) {
            $this->sendResponse(['error' => 'User ID is required'], 400);
            return;
        }

        if (!$semester || !$schoolyr) {
            $this->sendResponse(['error' => 'Semester and school year are required for approval'], 400);
            return;
        }

        try {
            $stmt = $this->conn->prepare("UPDATE user_account SET is_approved = 1, semester = ?, schoolyr = ? WHERE user_id = ?");
            $stmt->execute([$semester, $schoolyr, $userId]);

            if ($stmt->rowCount() > 0) {
                $this->sendResponse(['success' => true, 'message' => 'User approved successfully']);
            } else {
                $this->sendResponse(['error' => 'User not found or already approved'], 404);
            }
        } catch (Exception $e) {
            $this->sendResponse(['error' => 'Failed to approve user: ' . $e->getMessage()], 500);
        }
    }

    private function rejectUser() {
        $input = json_decode(file_get_contents('php://input'), true);
        $userId = $input['user_id'] ?? 0;

        if (!$userId) {
            $this->sendResponse(['error' => 'User ID is required'], 400);
            return;
        }

        try {
            // For rejection, we can either set is_approved to -1 or delete the account
            // Let's set it to -1 to indicate rejection
            $stmt = $this->conn->prepare("UPDATE user_account SET is_approved = -1 WHERE user_id = ?");
            $stmt->execute([$userId]);

            if ($stmt->rowCount() > 0) {
                $this->sendResponse(['success' => true, 'message' => 'User account rejected']);
            } else {
                $this->sendResponse(['error' => 'User not found'], 404);
            }
        } catch (Exception $e) {
            $this->sendResponse(['error' => 'Failed to reject user: ' . $e->getMessage()], 500);
        }
    }

    private function bulkApprove() {
        $input = json_decode(file_get_contents('php://input'), true);
        $userIds = $input['user_ids'] ?? [];
        $semester = $input['semester'] ?? null;
        $schoolyr = $input['schoolyr'] ?? null;

        if (empty($userIds) || !is_array($userIds)) {
            $this->sendResponse(['error' => 'User IDs array is required'], 400);
            return;
        }

        if (!$semester || !$schoolyr) {
            $this->sendResponse(['error' => 'Semester and school year are required for approval'], 400);
            return;
        }

        try {
            $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
            $stmt = $this->conn->prepare("UPDATE user_account SET is_approved = 1, semester = ?, schoolyr = ? WHERE user_id IN ($placeholders)");
            $params = array_merge([$semester, $schoolyr], $userIds);
            $stmt->execute($params);

            $this->sendResponse(['success' => true, 'message' => 'Users approved successfully', 'affected_rows' => $stmt->rowCount()]);
        } catch (Exception $e) {
            $this->sendResponse(['error' => 'Failed to approve users: ' . $e->getMessage()], 500);
        }
    }

    private function bulkDisapprove() {
        $input = json_decode(file_get_contents('php://input'), true);
        $userIds = $input['user_ids'] ?? [];

        if (empty($userIds) || !is_array($userIds)) {
            $this->sendResponse(['error' => 'User IDs array is required'], 400);
            return;
        }

        try {
            $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
            $stmt = $this->conn->prepare("UPDATE user_account SET is_approved = -1 WHERE user_id IN ($placeholders)");
            $stmt->execute($userIds);

            $this->sendResponse(['success' => true, 'message' => 'Users disapproved successfully', 'affected_rows' => $stmt->rowCount()]);
        } catch (Exception $e) {
            $this->sendResponse(['error' => 'Failed to disapprove users: ' . $e->getMessage()], 500);
        }
    }

    private function editUser() {
        $input = json_decode(file_get_contents('php://input'), true);
        $userId = $input['user_id'] ?? 0;
        $username = $input['username'] ?? null;
        $email = $input['email'] ?? null;
        $name = $input['name'] ?? null;
        $bio = $input['bio'] ?? null;
        $semester = $input['semester'] ?? null;
        $schoolyr = $input['schoolyr'] ?? null;

        if (!$userId) {
            $this->sendResponse(['error' => 'User ID is required'], 400);
            return;
        }

        try {
            // Build dynamic query based on provided fields
            $fields = [];
            $params = [];

            if ($username !== null) {
                $fields[] = 'username = ?';
                $params[] = $username;
            }
            if ($email !== null) {
                $fields[] = 'email = ?';
                $params[] = $email;
            }
            if ($name !== null) {
                $fields[] = 'name = ?';
                $params[] = $name;
            }
            if ($bio !== null) {
                $fields[] = 'bio = ?';
                $params[] = $bio;
            }
            if ($semester !== null) {
                $fields[] = 'semester = ?';
                $params[] = $semester;
            }
            if ($schoolyr !== null) {
                $fields[] = 'schoolyr = ?';
                $params[] = $schoolyr;
            }

            if (empty($fields)) {
                $this->sendResponse(['error' => 'No fields to update'], 400);
                return;
            }

            $params[] = $userId;
            $query = "UPDATE user_account SET " . implode(', ', $fields) . " WHERE user_id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);

            if ($stmt->rowCount() > 0) {
                $this->sendResponse(['success' => true, 'message' => 'User updated successfully']);
            } else {
                $this->sendResponse(['error' => 'User not found or no changes made'], 404);
            }
        } catch (Exception $e) {
            $this->sendResponse(['error' => 'Failed to update user: ' . $e->getMessage()], 500);
        }
    }

    private function bulkActivate() {
        $input = json_decode(file_get_contents('php://input'), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->sendResponse(['error' => 'Invalid JSON data'], 400);
            return;
        }

        $userIds = $input['user_ids'] ?? [];

        if (empty($userIds) || !is_array($userIds)) {
            $this->sendResponse(['error' => 'No valid user IDs provided'], 400);
            return;
        }

        // Validate that all user IDs are integers
        foreach ($userIds as $userId) {
            if (!is_numeric($userId) || $userId <= 0) {
                $this->sendResponse(['error' => 'Invalid user ID provided'], 400);
                return;
            }
        }

        try {
            $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
            $query = "UPDATE user_account SET is_banned = 0 WHERE user_id IN ($placeholders)";
            $stmt = $this->conn->prepare($query);

            if (!$stmt) {
                throw new Exception('Failed to prepare statement: ' . $this->conn->errorInfo()[2]);
            }

            $result = $stmt->execute($userIds);

            if (!$result) {
                throw new Exception('Failed to execute statement: ' . $stmt->errorInfo()[2]);
            }

            $affectedRows = $stmt->rowCount();
            $this->sendResponse([
                'success' => true,
                'message' => "$affectedRows users activated successfully",
                'affected_rows' => $affectedRows
            ]);
        } catch (Exception $e) {
            error_log('Bulk activate error: ' . $e->getMessage());
            $this->sendResponse(['error' => 'Failed to activate users: ' . $e->getMessage()], 500);
        }
    }

    private function bulkDeactivate() {
        $input = json_decode(file_get_contents('php://input'), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->sendResponse(['error' => 'Invalid JSON data'], 400);
            return;
        }

        $userIds = $input['user_ids'] ?? [];

        if (empty($userIds) || !is_array($userIds)) {
            $this->sendResponse(['error' => 'No valid user IDs provided'], 400);
            return;
        }

        // Validate that all user IDs are integers
        foreach ($userIds as $userId) {
            if (!is_numeric($userId) || $userId <= 0) {
                $this->sendResponse(['error' => 'Invalid user ID provided'], 400);
                return;
            }
        }

        try {
            $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
            $query = "UPDATE user_account SET is_banned = 1 WHERE user_id IN ($placeholders)";
            $stmt = $this->conn->prepare($query);

            if (!$stmt) {
                throw new Exception('Failed to prepare statement: ' . $this->conn->errorInfo()[2]);
            }

            $result = $stmt->execute($userIds);

            if (!$result) {
                throw new Exception('Failed to execute statement: ' . $stmt->errorInfo()[2]);
            }

            $affectedRows = $stmt->rowCount();
            $this->sendResponse([
                'success' => true,
                'message' => "$affectedRows users deactivated successfully",
                'affected_rows' => $affectedRows
            ]);
        } catch (Exception $e) {
            error_log('Bulk deactivate error: ' . $e->getMessage());
            $this->sendResponse(['error' => 'Failed to deactivate users: ' . $e->getMessage()], 500);
        }
    }

    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}

// Initialize and handle the request
$api = new UsersAPI();
$api->handleRequest();
