<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>User Management</title>
    <link rel="stylesheet" href="../../css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        html, body {
            max-width: 100%;
            overflow-x: hidden;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> Admin Panel</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active">
                    <a href="#">
                        <i class="fas fa-users"></i> Users Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="game-management.html">
                        <i class="fas fa-gamepad"></i> Game Content
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Users Management Section -->
            <section id="users-section" class="content-section active">
                <div class="section-header">
                    <h1>Users Management</h1>
                    <div class="search-bar">
                        <input type="text" id="userSearch" placeholder="Search users..." onkeyup="searchUsers()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>

                <!-- Unified Filter Menu -->
                <div class="unified-filter-menu">
                    <div class="filter-container">
                        <h3 class="main-filter-title">
                            <i class="fas fa-filter"></i>
                            User Filters
                        </h3>

                        <div class="filter-groups">
                            <!-- Filter Visibility Control -->
                            <div class="filter-group dropdown-filter-group">
                                <h4 class="filter-group-title">Filter Options</h4>
                                <div class="dropdown-filter-container">
                                    <select id="filterVisibilityDropdown" class="filter-dropdown" onchange="toggleFilterVisibility(this.value)">
                                        <option value="show-all">Show All Filters</option>
                                        <option value="show-approval">Show Approval Filters Only</option>
                                        <option value="show-activation">Show Activation Filters Only</option>
                                        <option value="show-advanced">Show Advanced Filters Only</option>
                                        <option value="hide-all">Hide All Filters</option>
                                    </select>
                                    <i class="fas fa-chevron-down dropdown-icon"></i>
                                </div>
                            </div>

                            <!-- Approval Status Filters -->
                            <div class="filter-group">
                                <h4 class="filter-group-title">Approval Status</h4>
                                <div class="filter-tabs approval-tabs">
                                    <button class="filter-tab approval-tab active" data-status="all" onclick="filterByApprovalStatus('all')">
                                        <i class="fas fa-users"></i>
                                        <span>All Users</span>
                                        <span class="tab-count" id="allUsersCount">0</span>
                                    </button>
                                    <button class="filter-tab approval-tab" data-status="pending" onclick="filterByApprovalStatus('pending')">
                                        <i class="fas fa-clock"></i>
                                        <span>Pending</span>
                                        <span class="tab-count" id="pendingUsersCount">0</span>
                                    </button>
                                    <button class="filter-tab approval-tab" data-status="approved" onclick="filterByApprovalStatus('approved')">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Approved</span>
                                        <span class="tab-count" id="approvedUsersCount">0</span>
                                    </button>
                                    <button class="filter-tab approval-tab" data-status="rejected" onclick="filterByApprovalStatus('rejected')">
                                        <i class="fas fa-times-circle"></i>
                                        <span>Rejected</span>
                                        <span class="tab-count" id="rejectedUsersCount">0</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Account Status Filters -->
                            <div class="filter-group">
                                <h4 class="filter-group-title">Account Status</h4>
                                <div class="filter-tabs activation-tabs">
                                    <button class="filter-tab activation-tab active" data-activation="all" onclick="filterByActivationStatus('all')">
                                        <i class="fas fa-users-cog"></i>
                                        <span>All Accounts</span>
                                        <span class="tab-count" id="allAccountsCount">0</span>
                                    </button>
                                    <button class="filter-tab activation-tab" data-activation="active" onclick="filterByActivationStatus('active')">
                                        <i class="fas fa-user-check"></i>
                                        <span>Active</span>
                                        <span class="tab-count" id="activeUsersCount">0</span>
                                    </button>
                                    <button class="filter-tab activation-tab" data-activation="deactivated" onclick="filterByActivationStatus('deactivated')">
                                        <i class="fas fa-user-slash"></i>
                                        <span>Deactivated</span>
                                        <span class="tab-count" id="deactivatedUsersCount">0</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Advanced Filters -->
                            <div class="filter-group advanced-filters" id="advancedFilters">
                                <h4 class="filter-group-title">Advanced Filters</h4>
                                <div class="advanced-filter-controls">
                                    <div class="filter-control-group">
                                        <label for="dateRangeFilter" class="filter-label">Registration Date:</label>
                                        <select id="dateRangeFilter" class="advanced-filter-select" onchange="applyDateFilter(this.value)">
                                            <option value="all">All Dates</option>
                                            <option value="today">Today</option>
                                            <option value="week">Last 7 Days</option>
                                            <option value="month">Last 30 Days</option>
                                            <option value="quarter">Last 3 Months</option>
                                            <option value="year">Last Year</option>
                                        </select>
                                    </div>
                                    <div class="filter-control-group">
                                        <label for="schoolYearFilter" class="filter-label">School Year:</label>
                                        <select id="schoolYearFilter" class="advanced-filter-select" onchange="applySchoolYearFilter(this.value)">
                                            <option value="all">All School Years</option>
                                        </select>
                                    </div>
                                    <div class="filter-control-group">
                                        <button type="button" class="clear-advanced-filters-btn" onclick="clearAdvancedFilters()">
                                            <i class="fas fa-times"></i>
                                            Clear Advanced Filters
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Bulk Operations -->
                <div class="bulk-operations" id="bulkOperations" style="display: none;">
                    <div class="bulk-controls">
                        <span id="selectedCount">0 users selected</span>

                        <!-- Approval Operations -->
                        <div class="bulk-group approval-operations" id="approvalOperations" style="display: none;">
                            <button id="bulkApproveBtn" class="bulk-btn approve-btn" onclick="handleBulkApprove()" style="display: none;">
                                <i class="fas fa-check"></i> Bulk Approve
                            </button>
                            <button id="bulkDisapproveBtn" class="bulk-btn reject-btn" onclick="handleBulkDisapprove()" style="display: none;">
                                <i class="fas fa-times"></i> Bulk Disapprove
                            </button>
                        </div>

                        <!-- Activation Operations -->
                        <div class="bulk-group activation-operations" id="activationOperations" style="display: none;">
                            <button id="bulkActivateBtn" class="bulk-btn activate-btn" onclick="handleBulkActivate()" style="display: none;">
                                <i class="fas fa-user-check"></i> Bulk Activate
                            </button>
                            <button id="bulkDeactivateBtn" class="bulk-btn deactivate-btn" onclick="handleBulkDeactivate()" style="display: none;">
                                <i class="fas fa-user-slash"></i> Bulk Deactivate
                            </button>
                        </div>

                        <button id="clearSelectionBtn" class="bulk-btn clear-btn" onclick="clearSelection()">
                            <i class="fas fa-times-circle"></i> Clear Selection
                        </button>
                    </div>
                </div>

                <!-- Users Grid -->
                <div class="users-grid" id="usersGrid">
                    <!-- Users will be populated by JavaScript -->
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <button id="prevBtn" onclick="changePage(-1)"><i class="fas fa-chevron-left"></i></button>
                    <span id="pageInfo">Page 1 of 1</span>
                    <button id="nextBtn" onclick="changePage(1)"><i class="fas fa-chevron-right"></i></button>
                </div>
            </section>
        </main>
    </div>

    <!-- User Details Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>User Details</h2>
                <span class="close" onclick="closeModal('userModal')">&times;</span>
            </div>
            <div class="modal-actions">
                <button id="editUserBtn" class="action-btn edit-btn"><i class="fas fa-edit"></i> Edit User</button>
                <button id="approveUserBtn" class="action-btn approve-btn" style="display: none;"><i class="fas fa-check"></i> Approve Account</button>
                <button id="rejectUserBtn" class="action-btn reject-btn" style="display: none;"><i class="fas fa-times"></i> Reject Account</button>
                <button id="activateUserBtn" class="action-btn activate-btn" style="display: none;"><i class="fas fa-check-circle"></i> Activate Account</button>
                <button id="deleteUserBtn" class="action-btn delete-btn"><i class="fas fa-trash"></i> Delete User</button>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- User details will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Bulk Approval Modal -->
    <div id="bulkApprovalModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Bulk Approve Users</h2>
                <span class="close" onclick="closeModal('bulkApprovalModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="bulkApprovalForm">
                    <div class="form-group">
                        <label for="bulkSemester">Semester:</label>
                        <select id="bulkSemester" name="semester" required>
                            <option value="">Select Semester</option>
                            <option value="1">1st Semester</option>
                            <option value="2">2nd Semester</option>
                            <option value="3">3rd Semester</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bulkSchoolyr">School Year:</label>
                        <select id="bulkSchoolyr" name="schoolyr" required>
                            <option value="">Select School Year</option>
                            <option value="2023-2024">2023-2024</option>
                            <option value="2024-2025">2024-2025</option>
                            <option value="2025-2026">2025-2026</option>
                            <option value="2026-2027">2026-2027</option>
                            <option value="2027-2028">2027-2028</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="confirmBulkApproval()" class="modal-btn approve-btn">
                            <i class="fas fa-check"></i>
                            Approve Selected Users
                        </button>
                        <button type="button" onclick="closeModal('bulkApprovalModal')" class="modal-btn cancel-btn">
                            <i class="fas fa-times"></i>
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Edit User</h2>
                <span class="close" onclick="closeModal('editUserModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId" name="user_id">
                    <div class="form-group">
                        <label for="editUsername">Username:</label>
                        <input type="text" id="editUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmail">Email:</label>
                        <input type="email" id="editEmail" name="email">
                    </div>
                    <div class="form-group">
                        <label for="editName">Name:</label>
                        <input type="text" id="editName" name="name">
                    </div>
                    <div class="form-group">
                        <label for="editBio">Bio:</label>
                        <textarea id="editBio" name="bio" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editSemester">Semester:</label>
                        <select id="editSemester" name="semester">
                            <option value="">Select Semester</option>
                            <option value="1">1st Semester</option>
                            <option value="2">2nd Semester</option>
                            <option value="3">3rd Semester</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editSchoolyr">School Year:</label>
                        <select id="editSchoolyr" name="schoolyr">
                            <option value="">Select School Year</option>
                            <option value="2023-2024">2023-2024</option>
                            <option value="2024-2025">2024-2025</option>
                            <option value="2025-2026">2025-2026</option>
                            <option value="2026-2027">2026-2027</option>
                            <option value="2027-2028">2027-2028</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="saveUserEdit()" class="modal-btn save-btn">
                            <i class="fas fa-save"></i>
                            Save Changes
                        </button>
                        <button type="button" onclick="closeModal('editUserModal')" class="modal-btn cancel-btn">
                            <i class="fas fa-times"></i>
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Approval Modal -->
    <div id="approvalModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Approve User</h2>
                <span class="close" onclick="closeModal('approvalModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="approvalForm">
                    <input type="hidden" id="approvalUserId" name="user_id">
                    <div class="form-group">
                        <label for="approvalSemester">Semester:</label>
                        <select id="approvalSemester" name="semester" required>
                            <option value="">Select Semester</option>
                            <option value="1">1st Semester</option>
                            <option value="2">2nd Semester</option>
                            <option value="3">3rd Semester</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="approvalSchoolyr">School Year:</label>
                        <select id="approvalSchoolyr" name="schoolyr" required>
                            <option value="">Select School Year</option>
                            <option value="2023-2024">2023-2024</option>
                            <option value="2024-2025">2024-2025</option>
                            <option value="2025-2026">2025-2026</option>
                            <option value="2026-2027">2026-2027</option>
                            <option value="2027-2028">2027-2028</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="confirmApproval()" class="modal-btn approve-btn">
                            <i class="fas fa-check"></i>
                            Approve User
                        </button>
                        <button type="button" onclick="closeModal('approvalModal')" class="modal-btn cancel-btn">
                            <i class="fas fa-times"></i>
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/admin/user-management.js"></script>
</body>
</html>
