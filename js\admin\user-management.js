// Global variables
let users = [];
let filteredUsers = [];
let currentPage = 1;
let usersPerPage = 50;
let expLevels = []; // Store experience levels from database
let maxExpLevel = 10; // Default value, will be updated from database

// Global state for modals and selections
let selectedUsers = new Set();
let currentEditUserId = null;

// Filtering state
let currentApprovalFilter = 'all';
let currentActivationFilter = 'all';

// API Configuration

// API Configuration
const API_BASE_URL = '../../php/admin/';

// Initialize data from database
async function initializeData() {
    try {
        // Load experience levels first to determine max level
        await loadExpLevels();
        await loadUsers();
        updateAllCounts();
        displayUsers();
        // Add event listeners for experience stats hover effects
        addExpHoverEffects();
        // Initialize school year options
        initializeSchoolYearOptions();
    } catch (error) {
        console.error('Error initializing data:', error);
        showNotification('Error loading data from database', 'error');
    }
}

// Load experience levels from database
async function loadExpLevels() {
    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=get_exp_levels`);
        const result = await response.json();

        if (result.success && result.data.length > 0) {
            expLevels = result.data;
            
            // Find the highest experience level
            const highestLevel = expLevels.reduce((max, level) => {
                const levelNum = parseInt(level.expName);
                return levelNum > max ? levelNum : max;
            }, 0);
            
            maxExpLevel = highestLevel;
            console.log(`Maximum experience level set to: ${maxExpLevel}`);
        } else {
            console.warn('No experience levels found, using default max level');
        }
    } catch (error) {
        console.error('Error loading experience levels:', error);
        // Keep default maxExpLevel if there's an error
    }
}

function mapBackendUserToFrontend(backendUser) {
    const expLevel = parseInt(backendUser.expLevel) || 1;
    const exp = parseInt(backendUser.userExp) || 0;
    
    return {
        id: backendUser.user_id,
        username: backendUser.username,
        email: backendUser.email || 'N/A',
        name: backendUser.name || 'N/A',
        bio: backendUser.bio || 'No bio available',
        avatar: backendUser.avatar || null,
        password: '********', // Never show actual passwords
        achievements: parseInt(backendUser.achievements) || 0,
        totalAchievements: parseInt(backendUser.totalAchievements) || 50,
        levelsCompleted: parseInt(backendUser.levels_completed) || 0,
        totalLevels: parseInt(backendUser.total_levels) || 6,
        exp: exp,
        expLevel: expLevel,
        isBanned: backendUser.is_banned == 1,
        isApproved: parseInt(backendUser.is_approved) || 0,
        semester: backendUser.semester || null,
        schoolyr: backendUser.schoolyr || null,
        ...calculateExpThresholds(exp, expLevel),
        joinDate: new Date(backendUser.created_at)
    };
}

function getApprovalStatus(isApproved) {
    switch (isApproved) {
        case 1:
            return {
                status: 'approved',
                text: 'Approved',
                badge: `<div class="status-badge approved-badge">
                    <i class="fas fa-check-circle"></i>
                    <span>Approved</span>
                </div>`
            };
        case -1:
            return {
                status: 'rejected',
                text: 'Rejected',
                badge: `<div class="status-badge rejected-badge">
                    <i class="fas fa-times-circle"></i>
                    <span>Rejected</span>
                </div>`
            };
        case 0:
        default:
            return {
                status: 'pending',
                text: 'Pending Approval',
                badge: `<div class="status-badge pending-badge">
                    <i class="fas fa-clock"></i>
                    <span>Pending</span>
                </div>`
            };
    }
}

// API Functions
async function loadUsers() {
    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=get_all_users`);
        const result = await response.json();

        if (result.success) {
            users = result.data.map(mapBackendUserToFrontend);
            applyCurrentFilters();
        } else {
            throw new Error(result.error || 'Failed to load users');
        }
    } catch (error) {
        console.error('Error loading users:', error);
        // Fallback to empty array
        users = [];
        filteredUsers = [];
    }
}

// Initialize school year options dynamically (only 2 options based on current year)
function initializeSchoolYearOptions() {
    const currentYear = new Date().getFullYear();
    const schoolYearSelects = ['bulkSchoolyr', 'editSchoolyr', 'approvalSchoolyr', 'schoolYearFilter'];

    schoolYearSelects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Clear existing options except the first one
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }

            // Add only 2 school year options based on current year
            // Option 1: Previous year to current year (e.g., 2015-2016 if current year is 2016)
            const prevSchoolYear = `${currentYear - 1}-${currentYear}`;
            const option1 = document.createElement('option');
            option1.value = prevSchoolYear;
            option1.textContent = prevSchoolYear;
            select.appendChild(option1);

            // Option 2: Current year to next year (e.g., 2016-2017 if current year is 2016)
            const currentSchoolYear = `${currentYear}-${currentYear + 1}`;
            const option2 = document.createElement('option');
            option2.value = currentSchoolYear;
            option2.textContent = currentSchoolYear;

            // Set current school year as default only for form selects, not for filter
            if (selectId !== 'schoolYearFilter') {
                option2.selected = true;
            }

            select.appendChild(option2);
        }
    });
}

// Filter visibility control
function toggleFilterVisibility(visibilityOption) {
    const approvalGroup = document.querySelector('.filter-group:has(.approval-tabs)');
    const activationGroup = document.querySelector('.filter-group:has(.activation-tabs)');
    const advancedGroup = document.getElementById('advancedFilters');

    // Hide all groups first
    if (approvalGroup) approvalGroup.style.display = 'none';
    if (activationGroup) activationGroup.style.display = 'none';
    if (advancedGroup) advancedGroup.style.display = 'none';

    switch (visibilityOption) {
        case 'show-all':
            if (approvalGroup) approvalGroup.style.display = 'block';
            if (activationGroup) activationGroup.style.display = 'block';
            if (advancedGroup) advancedGroup.style.display = 'block';
            showNotification('All filters are now visible', 'info');
            break;
        case 'show-approval':
            if (approvalGroup) approvalGroup.style.display = 'block';
            showNotification('Showing approval filters only', 'info');
            break;
        case 'show-activation':
            if (activationGroup) activationGroup.style.display = 'block';
            showNotification('Showing activation filters only', 'info');
            break;
        case 'show-advanced':
            if (advancedGroup) advancedGroup.style.display = 'block';
            showNotification('Showing advanced filters only', 'info');
            break;
        case 'hide-all':
            showNotification('All filters are now hidden', 'info');
            break;
    }
}

// Advanced filter functions
let currentDateFilter = 'all';
let currentSchoolYearFilter = 'all';

function applyDateFilter(dateRange) {
    currentDateFilter = dateRange;
    applyCurrentFilters();
    currentPage = 1;
    displayUsers();

    const dateNames = {
        'all': 'All Dates',
        'today': 'Today',
        'week': 'Last 7 Days',
        'month': 'Last 30 Days',
        'quarter': 'Last 3 Months',
        'year': 'Last Year'
    };

    if (dateRange !== 'all') {
        showNotification(`Filtered by registration date: ${dateNames[dateRange]}`, 'info');
    }
}

function applySchoolYearFilter(schoolYear) {
    currentSchoolYearFilter = schoolYear;
    applyCurrentFilters();
    currentPage = 1;
    displayUsers();

    if (schoolYear !== 'all') {
        showNotification(`Filtered by school year: ${schoolYear}`, 'info');
    }
}

function clearAdvancedFilters() {
    currentDateFilter = 'all';
    currentSchoolYearFilter = 'all';

    document.getElementById('dateRangeFilter').value = 'all';
    document.getElementById('schoolYearFilter').value = 'all';

    applyCurrentFilters();
    currentPage = 1;
    displayUsers();

    showNotification('Advanced filters cleared', 'info');
}

function applyDateRangeFilter(users, dateRange) {
    if (dateRange === 'all') return users;

    const now = new Date();
    let startDate;

    switch (dateRange) {
        case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
        case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case 'month':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
        case 'quarter':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
        case 'year':
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
        default:
            return users;
    }

    return users.filter(user => {
        const joinDate = new Date(user.joinDate);
        return joinDate >= startDate;
    });
}

function updateTabStates() {
    // Update approval tabs
    document.querySelectorAll('.approval-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    const approvalTab = document.querySelector(`[data-status="${currentApprovalFilter}"]`);
    if (approvalTab) {
        approvalTab.classList.add('active');
    }

    // Update activation tabs
    document.querySelectorAll('.activation-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    const activationTab = document.querySelector(`[data-activation="${currentActivationFilter}"]`);
    if (activationTab) {
        activationTab.classList.add('active');
    }
}

// Approval status filtering functions
function filterByApprovalStatus(status) {
    currentApprovalFilter = status;

    // Update active tab
    document.querySelectorAll('.approval-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-status="${status}"]`).classList.add('active');

    // Clear selection when changing filters
    clearSelection();

    // Apply filters and display
    applyCurrentFilters();
    currentPage = 1;
    displayUsers();

    // Update bulk operations UI for new filter
    updateBulkOperationsUI();
}

function applyCurrentFilters() {
    let filtered = [...users];

    // Apply approval status filter
    if (currentApprovalFilter !== 'all') {
        filtered = filtered.filter(user => {
            switch (currentApprovalFilter) {
                case 'pending':
                    return user.isApproved === 0;
                case 'approved':
                    return user.isApproved === 1;
                case 'rejected':
                    return user.isApproved === -1;
                default:
                    return true;
            }
        });
    }

    // Apply activation status filter
    if (currentActivationFilter !== 'all') {
        filtered = filtered.filter(user => {
            switch (currentActivationFilter) {
                case 'active':
                    return !user.isBanned;
                case 'deactivated':
                    return user.isBanned;
                default:
                    return true;
            }
        });
    }

    // Apply date range filter
    if (currentDateFilter !== 'all') {
        filtered = applyDateRangeFilter(filtered, currentDateFilter);
    }

    // Apply school year filter
    if (currentSchoolYearFilter !== 'all') {
        filtered = filtered.filter(user => {
            return user.schoolyr === currentSchoolYearFilter;
        });
    }

    filteredUsers = filtered;
    updateAllCounts();
}

// Activation status filtering function
function filterByActivationStatus(status) {
    currentActivationFilter = status;

    // Update active tab
    document.querySelectorAll('.activation-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-activation="${status}"]`).classList.add('active');

    // Clear selection when changing filters
    clearSelection();

    // Apply filters and display
    applyCurrentFilters();
    currentPage = 1;
    displayUsers();

    // Update bulk operations UI for new filter
    updateBulkOperationsUI();
}

function updateApprovalCounts() {
    const counts = {
        all: users.length,
        pending: users.filter(user => user.isApproved === 0).length,
        approved: users.filter(user => user.isApproved === 1).length,
        rejected: users.filter(user => user.isApproved === -1).length
    };

    // Update count displays
    document.getElementById('allUsersCount').textContent = counts.all;
    document.getElementById('pendingUsersCount').textContent = counts.pending;
    document.getElementById('approvedUsersCount').textContent = counts.approved;
    document.getElementById('rejectedUsersCount').textContent = counts.rejected;
}

function updateActivationCounts() {
    const counts = {
        all: users.length,
        active: users.filter(user => !user.isBanned).length,
        deactivated: users.filter(user => user.isBanned).length
    };

    // Update count displays
    document.getElementById('allAccountsCount').textContent = counts.all;
    document.getElementById('activeUsersCount').textContent = counts.active;
    document.getElementById('deactivatedUsersCount').textContent = counts.deactivated;
}

function updateAllCounts() {
    updateApprovalCounts();
    updateActivationCounts();
}

// Users management functions
function displayUsers() {
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const usersToShow = filteredUsers.slice(startIndex, endIndex);

    const usersGrid = document.getElementById('usersGrid');
    usersGrid.innerHTML = '';

    if (usersToShow.length === 0) {
        usersGrid.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>No Users Found</h3>
                <p>No users match your search criteria or no users exist yet.</p>
            </div>
        `;
        updatePagination();
        return;
    }

    usersToShow.forEach(user => {
        const userCard = createUserCard(user);
        usersGrid.appendChild(userCard);
    });

    updatePagination();

    // Re-add hover effects after displaying users
    addExpHoverEffects();

    // Restore selection state for displayed users
    restoreSelectionState();
}

function createUserCard(user) {
    // Check if ban has expired
    let isActuallyBanned = user.isBanned;
    if (user.isBanned && user.bannedUntil) {
        const now = new Date();
        const bannedUntilDate = new Date(user.bannedUntil);
        if (bannedUntilDate < now) {
            isActuallyBanned = false;
        }
    }

    // Determine approval status
    const approvalStatus = getApprovalStatus(user.isApproved);

    const card = document.createElement('div');
    card.className = 'user-card';
    if (isActuallyBanned) {
        card.classList.add('banned-card');
    }

    // Add click handler that ignores clicks on checkbox
    card.onclick = (event) => {
        // Don't show modal if clicking on checkbox or its container
        if (event.target.type === 'checkbox' ||
            event.target.classList.contains('user-checkbox') ||
            event.target.classList.contains('user-selection') ||
            event.target.closest('.user-selection')) {
            return;
        }
        showUserDetails(user);
    };

    // Calculate experience percentage using the pre-calculated values
    const expPercentage = ((user.exp - user.currentLevelExp) / (user.nextLevelExp - user.currentLevelExp)) * 100;

    // Format join date
    const joinDate = user.joinDate ? user.joinDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }) : 'Unknown';

    // Get user avatar or default
    const avatarSrc = user.avatar || '../../images/profile-icon.png';

    card.innerHTML = `
        <!-- Selection Checkbox - show for specific filters -->
        ${(currentApprovalFilter !== 'all' || currentActivationFilter !== 'all') ? `
            <div class="user-selection" onclick="event.stopPropagation()">
                <input type="checkbox" class="user-checkbox" data-user-id="${user.id}" onchange="toggleUserSelection(${user.id}, this.checked)" onclick="event.stopPropagation()">
            </div>
        ` : ''}

        <!-- User Profile Section -->
        <div class="user-profile-section">
            <div class="user-avatar">
                <img src="${avatarSrc}" alt="${user.username}" onerror="this.src='../../images/profile-icon.png'">
                <div class="user-level-badge">${user.expLevel}</div>
            </div>
            <div class="user-basic-info">
                <h3 class="user-name" title="${user.username}">${user.username}</h3>
                <p class="user-email" title="${user.email}">${user.email}</p>
                <div class="user-meta">
                    <span class="join-date">
                        <i class="fas fa-calendar-alt"></i>
                        Joined ${joinDate}
                    </span>
                    ${user.name && user.name !== 'N/A' ? `
                        <span class="full-name">
                            <i class="fas fa-user"></i>
                            ${user.name}
                        </span>
                    ` : ''}
                    <!-- Status Badges positioned below join date -->
                    <div class="user-status-info">
                        ${approvalStatus.badge}
                        ${isActuallyBanned ? `
                            <div class="status-badge deactivated-badge">
                                <i class="fas fa-ban"></i>
                                <span>Deactivated</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Stats -->
        <div class="user-progress-stats">
            <div class="progress-item">
                <div class="progress-icon achievements-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="progress-info">
                    <div class="progress-value">${user.achievements}/${user.totalAchievements}</div>
                    <div class="progress-label">Achievements</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${(user.achievements / user.totalAchievements) * 100}%"></div>
                    </div>
                </div>
            </div>

            <div class="progress-item">
                <div class="progress-icon levels-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <div class="progress-info">
                    <div class="progress-value">${user.levelsCompleted}/${user.totalLevels}</div>
                    <div class="progress-label">Levels Completed</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${(user.levelsCompleted / user.totalLevels) * 100}%"></div>
                    </div>
                </div>
            </div>

            <div class="progress-item experience-item">
                <div class="progress-icon experience-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="progress-info">
                    <div class="progress-value">
                        ${user.expLevel >= maxExpLevel ?
                          `${user.exp} EXP (MAX)` :
                          `${user.exp}/${user.nextLevelExp} EXP`
                        }
                    </div>
                    <div class="progress-label">Experience Level ${user.expLevel}</div>
                    <div class="progress-bar experience-bar">
                        <div class="progress-fill experience-fill" style="width: ${user.expLevel >= maxExpLevel ? 100 : expPercentage}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="user-quick-actions">
            <button class="quick-action-btn view-btn" onclick="event.stopPropagation(); showUserDetails(${JSON.stringify(user).replace(/"/g, '&quot;')})" title="View Details">
                <i class="fas fa-eye"></i>
                <span>View</span>
            </button>
            ${user.isApproved === 0 ? `
                <button class="quick-action-btn approve-btn" onclick="event.stopPropagation(); showApprovalModal(${user.id})" title="Approve User">
                    <i class="fas fa-check"></i>
                    <span>Approve</span>
                </button>
                <button class="quick-action-btn reject-btn" onclick="event.stopPropagation(); rejectUser(${user.id})" title="Reject User">
                    <i class="fas fa-times"></i>
                    <span>Reject</span>
                </button>
            ` : user.isApproved === 1 ? `
                ${isActuallyBanned ? `
                    <button class="quick-action-btn activate-btn" onclick="event.stopPropagation(); activateUser(${user.id})" title="Activate User">
                        <i class="fas fa-check-circle"></i>
                        <span>Activate</span>
                    </button>
                ` : `
                    <button class="quick-action-btn deactivate-btn" onclick="event.stopPropagation(); deactivateUser(${user.id})" title="Deactivate User">
                        <i class="fas fa-ban"></i>
                        <span>Deactivate</span>
                    </button>
                `}
                <button class="quick-action-btn edit-btn" onclick="event.stopPropagation(); showEditUserModal(${JSON.stringify(user).replace(/"/g, '&quot;')})" title="Edit User">
                    <i class="fas fa-edit"></i>
                    <span>Edit</span>
                </button>
            ` : `
                <button class="quick-action-btn delete-btn" onclick="event.stopPropagation(); deleteUser(${user.id})" title="Delete User">
                    <i class="fas fa-trash"></i>
                    <span>Delete</span>
                </button>
            `}
        </div>
    `;

    return card;
}

function toggleDropdown(event, userId) {
    event.stopPropagation();

    // Close all other dropdowns
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        if (menu.id !== `dropdown-${userId}`) {
            menu.classList.remove('show');
        }
    });

    // Toggle current dropdown
    const dropdown = document.getElementById(`dropdown-${userId}`);
    dropdown.classList.toggle('show');
}

function showUserDetails(user) {
    const isDeactivated = user.isBanned;
    const modal = document.getElementById('userModal');
    const modalBody = document.getElementById('userModalBody');
    const modalContent = modal.querySelector('.modal-content');

    // Remove existing deactivation banner if present
    const existingBanner = modalContent.querySelector('.deactivation-banner');
    if (existingBanner) {
        modalContent.removeChild(existingBanner);
    }

    // Add deactivation banner if user is deactivated
    if (isDeactivated) {
        const deactivationBanner = document.createElement('div');
        deactivationBanner.className = 'deactivation-banner';

        deactivationBanner.innerHTML = `
            <h3><i class="fas fa-ban"></i> Account Deactivated</h3>
            <p>This user's account has been deactivated and cannot access the system.</p>
        `;
        // Insert banner after the modal header
        modalContent.insertBefore(deactivationBanner, modalBody.parentNode.querySelector('.modal-actions'));
    }

    const achievementProgress = (user.achievements / user.totalAchievements) * 100;
    const levelProgress = (user.levelsCompleted / user.totalLevels) * 100;

    modalBody.innerHTML = `
        <div class="user-detail-container">
            <div class="user-detail-section">
                <h3>Basic Information</h3>
                <div class="user-basic-info">
                    ${user.avatar ? `
                    <div class="info-item">
                        <div class="info-label">Avatar</div>
                        <div class="info-value">
                            <img src="${user.avatar}" alt="User Avatar" style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;">
                        </div>
                    </div>` : ''}
                    <div class="info-item">
                        <div class="info-label">User ID</div>
                        <div class="info-value">${user.id}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Username</div>
                        <div class="info-value">${user.username}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Email</div>
                        <div class="info-value">${user.email}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Bio</div>
                        <div class="info-value">${user.bio}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Join Date</div>
                        <div class="info-value">${user.joinDate.toLocaleDateString()}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Semester</div>
                        <div class="info-value">${user.semester ? getSemesterText(user.semester) : 'Not set'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">School Year</div>
                        <div class="info-value">${user.schoolyr || 'Not set'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Account Status</div>
                        <div class="info-value">
                            ${(() => {
                                const approvalStatus = getApprovalStatus(user.isApproved);
                                let statusClass = '';
                                switch (approvalStatus.status) {
                                    case 'approved': statusClass = 'approved'; break;
                                    case 'rejected': statusClass = 'rejected'; break;
                                    case 'pending': statusClass = 'pending'; break;
                                }
                                return `<span class="approval-status-badge ${statusClass}">${approvalStatus.text}</span>`;
                            })()}
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Account Access</div>
                        <div class="info-value">
                            ${isDeactivated ? `
                                <span class="ban-status-badge banned">Deactivated</span>
                            ` : `
                                <span class="ban-status-badge">Active</span>
                            `}
                        </div>
                    </div>
                </div>
            </div>

            <div class="user-detail-section">
                <h3>Progress & Achievements</h3>
                <div class="progress-section">
                    <div class="progress-item">
                        <div class="progress-header">
                            <span class="progress-label">Achievements Unlocked</span>
                            <span class="progress-value">${user.achievements}/${user.totalAchievements}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${achievementProgress}%"></div>
                        </div>
                    </div>

                    <div class="progress-item">
                        <div class="progress-header">
                            <span class="progress-label">Levels Completed</span>
                            <span class="progress-value">${user.levelsCompleted}/${user.totalLevels}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${levelProgress}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="user-detail-section">
                <h3>User Experience</h3>
                <div class="exp-display">
                    <div class="exp-header">
                        <div class="exp-level">
                            <div class="exp-level-badge">${user.expLevel}</div>
                            <div class="exp-title">
                                Experience ${user.expLevel}
                                ${user.isMaxLevel ? ' <span class="max-level-tag">(MAX)</span>' : ''}
                            </div>
                        </div>
                        <div class="exp-value">${user.exp} EXP</div>
                    </div>
                    
                    <!-- Calculate experience percentage -->
                    ${(() => {
                        const expPercentage = ((user.exp - user.currentLevelExp) / (user.nextLevelExp - user.currentLevelExp)) * 100;
                        const expToNextLevel = user.nextLevelExp - user.exp;
                        
                        // Check if user has reached maximum level
                        const isMaxLevel = user.expLevel >= maxExpLevel;
                        
                        return `
                            <div class="exp-progress-container">
                                <div class="exp-progress-bar" style="width: ${isMaxLevel ? 100 : expPercentage}%"></div>
                            </div>
                            <div class="exp-progress-info">
                                <span>Current: ${user.exp} EXP</span>
                                ${isMaxLevel ?
                                    `<span class="exp-max-level"><i class="fas fa-trophy"></i> Maximum Level Reached!</span>` :
                                    `<span class="exp-next-level">${expToNextLevel} EXP needed to reach experience ${user.expLevel + 1}</span>`
                                }
                            </div>
                        `;
                    })()}
                </div>
            </div>
        </div>
    `;

    // Button and action handling
    const editBtn = document.getElementById('editUserBtn');
    const approveBtn = document.getElementById('approveUserBtn');
    const rejectBtn = document.getElementById('rejectUserBtn');
    const activateBtn = document.getElementById('activateUserBtn');
    const deleteBtn = document.getElementById('deleteUserBtn');

    // Edit button is always available
    editBtn.onclick = () => showEditUserModal(user);

    // Approval/rejection buttons - only show for pending users
    if (user.isApproved === 0) { // Pending approval
        approveBtn.style.display = 'inline-flex';
        rejectBtn.style.display = 'inline-flex';
        approveBtn.onclick = () => showApprovalModal(user.id);
        rejectBtn.onclick = () => rejectUser(user.id);
    } else {
        approveBtn.style.display = 'none';
        rejectBtn.style.display = 'none';
    }

    // Activate button - only show for approved and deactivated users
    if (user.isApproved === 1 && isDeactivated) {
        activateBtn.style.display = 'inline-flex';
        activateBtn.onclick = () => activateUser(user.id);
    } else {
        activateBtn.style.display = 'none';
    }

    deleteBtn.onclick = () => deleteUser(user.id);

    modal.classList.add('show');
}

async function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user?')) {
        try {
            const response = await fetch(`${API_BASE_URL}users_api.php?action=delete_user&user_id=${userId}`, {
                method: 'DELETE'
            });
            const result = await response.json();

            if (result.success) {
                users = users.filter(user => user.id !== userId);
                filteredUsers = filteredUsers.filter(user => user.id !== userId);
                displayUsers();
                closeModal('userModal'); // Close the user details modal
                showNotification('User deleted successfully', 'success');
            } else {
                showNotification(result.error || 'Failed to delete user', 'error');
            }
        } catch (error) {
            console.error('Error deleting user:', error);
            showNotification('Error deleting user', 'error');
        }

        // Close dropdown
        const dropdown = document.getElementById(`dropdown-${userId}`);
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }
}

async function searchUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();

    if (searchTerm.trim() === '') {
        applyCurrentFilters();
    } else {
        try {
            const response = await fetch(`${API_BASE_URL}users_api.php?action=search_users&search=${encodeURIComponent(searchTerm)}`);
            const result = await response.json();

            if (result.success) {
                filteredUsers = result.data.map(user => {
                    // Ensure experience data is properly formatted
                    const expLevel = parseInt(user.expLevel) || 1;
                    const exp = parseInt(user.userExp) || 0;
                    
                    return {
                        id: user.user_id,
                        username: user.username,
                        email: user.email || 'N/A',
                        name: user.name || 'N/A',
                        bio: user.bio || 'No bio available',
                        avatar: user.avatar || null,
                        password: '********',
                        achievements: parseInt(user.achievements) || 0,
                        totalAchievements: parseInt(user.totalAchievements) || 50,
                        levelsCompleted: parseInt(user.levels_completed) || 0,
                        totalLevels: parseInt(user.total_levels) || 6,
                        exp: exp,
                        expLevel: expLevel,
                        isBanned: user.is_banned == 1,
                        bannedFrom: user.banned_from,
                        bannedUntil: user.banned_until,
                        // Calculate experience thresholds based on database values
                        ...calculateExpThresholds(exp, expLevel),
                        joinDate: new Date(user.created_at)
                    };
                });

                // Apply approval status filter to search results
                if (currentApprovalFilter !== 'all') {
                    filteredUsers = filteredUsers.filter(user => {
                        switch (currentApprovalFilter) {
                            case 'pending':
                                return user.isApproved === 0;
                            case 'approved':
                                return user.isApproved === 1;
                            case 'rejected':
                                return user.isApproved === -1;
                            default:
                                return true;
                        }
                    });
                }

                // Apply activation status filter to search results
                if (currentActivationFilter !== 'all') {
                    filteredUsers = filteredUsers.filter(user => {
                        switch (currentActivationFilter) {
                            case 'active':
                                return !user.isBanned;
                            case 'deactivated':
                                return user.isBanned;
                            default:
                                return true;
                        }
                    });
                }
            } else {
                // Fallback to local search with approval filter
                let searchResults = users.filter(user =>
                    user.username.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm) ||
                    user.name.toLowerCase().includes(searchTerm)
                );

                // Apply approval status filter
                if (currentApprovalFilter !== 'all') {
                    searchResults = searchResults.filter(user => {
                        switch (currentApprovalFilter) {
                            case 'pending':
                                return user.isApproved === 0;
                            case 'approved':
                                return user.isApproved === 1;
                            case 'rejected':
                                return user.isApproved === -1;
                            default:
                                return true;
                        }
                    });
                }

                // Apply activation status filter
                if (currentActivationFilter !== 'all') {
                    searchResults = searchResults.filter(user => {
                        switch (currentActivationFilter) {
                            case 'active':
                                return !user.isBanned;
                            case 'deactivated':
                                return user.isBanned;
                            default:
                                return true;
                        }
                    });
                }

                filteredUsers = searchResults;
            }
        } catch (error) {
            console.error('Error searching users:', error);
            // Fallback to local search with approval filter
            let searchResults = users.filter(user =>
                user.username.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm) ||
                user.name.toLowerCase().includes(searchTerm)
            );

            // Apply approval status filter
            if (currentApprovalFilter !== 'all') {
                searchResults = searchResults.filter(user => {
                    switch (currentApprovalFilter) {
                        case 'pending':
                            return user.isApproved === 0;
                        case 'approved':
                            return user.isApproved === 1;
                        case 'rejected':
                            return user.isApproved === -1;
                        default:
                            return true;
                    }
                });
            }

            // Apply activation status filter
            if (currentActivationFilter !== 'all') {
                searchResults = searchResults.filter(user => {
                    switch (currentActivationFilter) {
                        case 'active':
                            return !user.isBanned;
                        case 'deactivated':
                            return user.isBanned;
                        default:
                            return true;
                    }
                });
            }

            filteredUsers = searchResults;
        }
    }

    currentPage = 1;
    displayUsers();
    
    // Re-add hover effects after search results are displayed
    addExpHoverEffects();
}

function changePage(direction) {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayUsers();
        // Re-add hover effects after pagination
        addExpHoverEffects();
    }
}

function updatePagination() {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    document.getElementById('pageInfo').textContent = `Page ${currentPage} of ${totalPages}`;
    document.getElementById('prevBtn').disabled = currentPage === 1;
    document.getElementById('nextBtn').disabled = currentPage === totalPages;
}

// Modal functions
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">&times;</button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Event listeners
document.addEventListener('DOMContentLoaded', async function() {
    await initializeData();

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.user-actions')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    // Close modals when clicking outside
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.classList.remove('show');
            }
        });
    });
});

// Add hover effects for experience stats
function addExpHoverEffects() {
    document.querySelectorAll('.exp-stat').forEach(stat => {
        stat.addEventListener('mouseenter', function() {
            const tooltip = this.querySelector('.exp-tooltip');
            if (tooltip) {
                tooltip.style.opacity = '1';
            }
        });
        
        stat.addEventListener('mouseleave', function() {
            const tooltip = this.querySelector('.exp-tooltip');
            if (tooltip) {
                tooltip.style.opacity = '0';
            }
        });
    });
}

// Calculate next level threshold based on experience
function calculateExpThresholds(currentExp, currentLevel) {
    // If no experience levels are loaded yet, use default calculation
    if (expLevels.length === 0) {
        return {
            nextLevelExp: currentLevel * 100,
            currentLevelExp: (currentLevel - 1) * 100,
            isMaxLevel: currentLevel >= maxExpLevel
        };
    }
    
    // Find the current level's experience threshold
    const currentLevelData = expLevels.find(level => parseInt(level.expName) === currentLevel);
    const currentLevelExp = currentLevelData ? parseInt(currentLevelData.expNeeded) : (currentLevel - 1) * 100;
    
    // Find the next level's experience threshold
    const nextLevel = currentLevel + 1;
    const nextLevelData = expLevels.find(level => parseInt(level.expName) === nextLevel);
    const nextLevelExp = nextLevelData ? parseInt(nextLevelData.expNeeded) : currentLevel * 100;
    
    return {
        nextLevelExp: nextLevelExp,
        currentLevelExp: currentLevelExp,
        isMaxLevel: currentLevel >= maxExpLevel
    };
}





// Reject User
async function rejectUser(userId) {
    if (confirm('Are you sure you want to reject this user account? This action cannot be undone.')) {
        try {
            const response = await fetch(`${API_BASE_URL}users_api.php?action=reject_user`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ user_id: userId })
            });
            const result = await response.json();

            if (result.success) {
                // Update user in local arrays
                const updateUser = (user) => {
                    if (user.id === userId) {
                        user.isApproved = -1;
                    }
                    return user;
                };
                users = users.map(updateUser);
                applyCurrentFilters();

                displayUsers();
                updateApprovalCounts();
                closeModal('userModal');
                showNotification('User account rejected', 'success');
            } else {
                showNotification(result.error || 'Failed to reject user', 'error');
            }
        } catch (error) {
            console.error('Error rejecting user:', error);
            showNotification('Error rejecting user', 'error');
        }
    }
}

// Bulk selection functions
function toggleUserSelection(userId, isSelected) {
    if (isSelected) {
        selectedUsers.add(userId);
    } else {
        selectedUsers.delete(userId);
    }

    // Update visual indication on the card
    const userCard = document.querySelector(`[data-user-id="${userId}"]`).closest('.user-card');
    if (userCard) {
        if (isSelected) {
            userCard.classList.add('selected');
        } else {
            userCard.classList.remove('selected');
        }
    }

    updateBulkOperationsUI();
}

function updateBulkOperationsUI() {
    const bulkOperations = document.getElementById('bulkOperations');
    const selectedCount = document.getElementById('selectedCount');
    const approvalOperations = document.getElementById('approvalOperations');
    const activationOperations = document.getElementById('activationOperations');
    const bulkApproveBtn = document.getElementById('bulkApproveBtn');
    const bulkDisapproveBtn = document.getElementById('bulkDisapproveBtn');
    const bulkActivateBtn = document.getElementById('bulkActivateBtn');
    const bulkDeactivateBtn = document.getElementById('bulkDeactivateBtn');

    if (selectedUsers.size > 0 && (currentApprovalFilter !== 'all' || currentActivationFilter !== 'all')) {
        bulkOperations.style.display = 'block';
        selectedCount.textContent = `${selectedUsers.size} user${selectedUsers.size > 1 ? 's' : ''} selected`;

        // Reset all buttons
        approvalOperations.style.display = 'none';
        activationOperations.style.display = 'none';
        bulkApproveBtn.style.display = 'none';
        bulkDisapproveBtn.style.display = 'none';
        bulkActivateBtn.style.display = 'none';
        bulkDeactivateBtn.style.display = 'none';

        // Show approval operations based on approval filter
        if (currentApprovalFilter !== 'all') {
            approvalOperations.style.display = 'flex';

            if (currentApprovalFilter === 'pending' || currentApprovalFilter === 'rejected') {
                bulkApproveBtn.style.display = 'inline-flex';
            } else if (currentApprovalFilter === 'approved') {
                bulkDisapproveBtn.style.display = 'inline-flex';
            }
        }

        // Show activation operations based on activation filter
        if (currentActivationFilter !== 'all') {
            activationOperations.style.display = 'flex';

            if (currentActivationFilter === 'deactivated') {
                bulkActivateBtn.style.display = 'inline-flex';
            } else if (currentActivationFilter === 'active') {
                bulkDeactivateBtn.style.display = 'inline-flex';
            }
        }
    } else {
        bulkOperations.style.display = 'none';
    }
}

function clearSelection() {
    selectedUsers.clear();
    // Uncheck all checkboxes and remove selection styling
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        const userCard = checkbox.closest('.user-card');
        if (userCard) {
            userCard.classList.remove('selected');
        }
    });
    updateBulkOperationsUI();
}

function restoreSelectionState() {
    // Restore checkbox states and visual indicators for selected users
    selectedUsers.forEach(userId => {
        const checkbox = document.querySelector(`[data-user-id="${userId}"]`);
        if (checkbox) {
            checkbox.checked = true;
            const userCard = checkbox.closest('.user-card');
            if (userCard) {
                userCard.classList.add('selected');
            }
        }
    });
}

// Context-aware bulk operation handlers
function handleBulkApprove() {
    if (selectedUsers.size === 0) {
        showNotification('Please select users to approve', 'warning');
        return;
    }

    if (currentApprovalFilter === 'pending' || currentApprovalFilter === 'rejected') {
        showBulkApprovalModal();
    }
}

function handleBulkDisapprove() {
    if (selectedUsers.size === 0) {
        showNotification('Please select users to disapprove', 'warning');
        return;
    }

    if (currentApprovalFilter === 'approved') {
        bulkDisapprove();
    }
}

function handleBulkActivate() {
    if (selectedUsers.size === 0) {
        showNotification('Please select users to activate', 'warning');
        return;
    }

    if (currentActivationFilter === 'deactivated') {
        bulkActivate();
    }
}

function handleBulkDeactivate() {
    if (selectedUsers.size === 0) {
        showNotification('Please select users to deactivate', 'warning');
        return;
    }

    if (currentActivationFilter === 'active') {
        bulkDeactivate();
    }
}

// Bulk approval functions
function showBulkApprovalModal() {
    document.getElementById('bulkApprovalModal').classList.add('show');
}

async function confirmBulkApproval() {
    const semester = document.getElementById('bulkSemester').value;
    const schoolyr = document.getElementById('bulkSchoolyr').value;

    if (!semester || !schoolyr) {
        showNotification('Please fill in all required fields', 'warning');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=bulk_approve`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                user_ids: Array.from(selectedUsers),
                semester: semester,
                schoolyr: schoolyr
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`${result.affected_rows} users approved successfully`, 'success');
            clearSelection();
            closeModal('bulkApprovalModal');
            await loadUsers();
            updateAllCounts();
            displayUsers();
        } else {
            showNotification(result.error || 'Failed to approve users', 'error');
        }
    } catch (error) {
        console.error('Error approving users:', error);
        showNotification('Error approving users', 'error');
    }
}

async function bulkDisapprove() {
    if (selectedUsers.size === 0) {
        showNotification('Please select users to disapprove', 'warning');
        return;
    }

    if (!confirm(`Are you sure you want to disapprove ${selectedUsers.size} user(s)?`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=bulk_disapprove`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                user_ids: Array.from(selectedUsers)
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`${result.affected_rows} users disapproved successfully`, 'success');
            clearSelection();
            await loadUsers();
            updateAllCounts();
            displayUsers();
        } else {
            showNotification(result.error || 'Failed to disapprove users', 'error');
        }
    } catch (error) {
        console.error('Error disapproving users:', error);
        showNotification('Error disapproving users', 'error');
    }
}

// Bulk activation functions
async function bulkActivate() {
    if (selectedUsers.size === 0) {
        showNotification('Please select users to activate', 'warning');
        return;
    }

    const userIds = Array.from(selectedUsers);

    try {
        const response = await fetch('../../php/admin/users_api.php?action=bulk_activate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_ids: userIds
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            showNotification(`${result.affected_rows} users activated successfully`, 'success');
            clearSelection();
            await loadUsers();
            updateAllCounts();
            displayUsers();
        } else {
            throw new Error(result.error || 'Failed to activate users');
        }
    } catch (error) {
        console.error('Error bulk activating users:', error);
        showNotification(`Error activating users: ${error.message}`, 'error');
    }
}

async function bulkDeactivate() {
    if (selectedUsers.size === 0) {
        showNotification('Please select users to deactivate', 'warning');
        return;
    }

    const userIds = Array.from(selectedUsers);

    if (!confirm(`Are you sure you want to deactivate ${userIds.length} user${userIds.length > 1 ? 's' : ''}? This will prevent them from accessing the system.`)) {
        return;
    }

    try {
        const response = await fetch('../../php/admin/users_api.php?action=bulk_deactivate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_ids: userIds
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            showNotification(`${result.affected_rows} users deactivated successfully`, 'success');
            clearSelection();
            await loadUsers();
            updateAllCounts();
            displayUsers();
        } else {
            throw new Error(result.error || 'Failed to deactivate users');
        }
    } catch (error) {
        console.error('Error bulk deactivating users:', error);
        showNotification(`Error deactivating users: ${error.message}`, 'error');
    }
}

// Edit user functions
function showEditUserModal(user) {
    currentEditUserId = user.id;

    // Populate form fields
    document.getElementById('editUserId').value = user.id;
    document.getElementById('editUsername').value = user.username;
    document.getElementById('editEmail').value = user.email === 'N/A' ? '' : user.email;
    document.getElementById('editName').value = user.name === 'N/A' ? '' : user.name;
    document.getElementById('editBio').value = user.bio === 'No bio available' ? '' : user.bio;
    document.getElementById('editSemester').value = user.semester || '';
    document.getElementById('editSchoolyr').value = user.schoolyr || '';

    document.getElementById('editUserModal').classList.add('show');
}

async function saveUserEdit() {
    const formData = {
        user_id: currentEditUserId,
        username: document.getElementById('editUsername').value,
        email: document.getElementById('editEmail').value,
        name: document.getElementById('editName').value,
        bio: document.getElementById('editBio').value,
        semester: document.getElementById('editSemester').value,
        schoolyr: document.getElementById('editSchoolyr').value
    };

    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=edit_user`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification('User updated successfully', 'success');
            closeModal('editUserModal');
            closeModal('userModal');
            await loadUsers();
            displayUsers();
        } else {
            showNotification(result.error || 'Failed to update user', 'error');
        }
    } catch (error) {
        console.error('Error updating user:', error);
        showNotification('Error updating user', 'error');
    }
}

// Approval functions
function showApprovalModal(userId) {
    document.getElementById('approvalUserId').value = userId;
    document.getElementById('approvalModal').classList.add('show');
}

async function confirmApproval() {
    const userId = document.getElementById('approvalUserId').value;
    const semester = document.getElementById('approvalSemester').value;
    const schoolyr = document.getElementById('approvalSchoolyr').value;

    if (!semester || !schoolyr) {
        showNotification('Please fill in all required fields', 'warning');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=approve_user`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                user_id: userId,
                semester: semester,
                schoolyr: schoolyr
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('User approved successfully', 'success');
            closeModal('approvalModal');
            closeModal('userModal');
            await loadUsers();
            updateApprovalCounts();
            displayUsers();
        } else {
            showNotification(result.error || 'Failed to approve user', 'error');
        }
    } catch (error) {
        console.error('Error approving user:', error);
        showNotification('Error approving user', 'error');
    }
}

// Activate/Deactivate functions
async function activateUser(userId) {
    if (!confirm('Are you sure you want to activate this user account?')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=activate_user&user_id=${userId}`, {
            method: 'POST'
        });
        const result = await response.json();

        if (result.success) {
            showNotification('User activated successfully', 'success');
            closeModal('userModal');
            await loadUsers();
            updateApprovalCounts();
            displayUsers();
        } else {
            showNotification(result.error || 'Failed to activate user', 'error');
        }
    } catch (error) {
        console.error('Error activating user:', error);
        showNotification('Error activating user', 'error');
    }
}

async function deactivateUser(userId) {
    if (!confirm('Are you sure you want to deactivate this user account?')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=deactivate_user&user_id=${userId}`, {
            method: 'POST'
        });
        const result = await response.json();

        if (result.success) {
            showNotification('User deactivated successfully', 'success');
            closeModal('userModal');
            await loadUsers();
            updateApprovalCounts();
            displayUsers();
        } else {
            showNotification(result.error || 'Failed to deactivate user', 'error');
        }
    } catch (error) {
        console.error('Error deactivating user:', error);
        showNotification('Error deactivating user', 'error');
    }
}

// Helper functions
function getSemesterText(semester) {
    switch(semester) {
        case '1': return '1st Semester';
        case '2': return '2nd Semester';
        case '3': return '3rd Semester';
        default: return 'Unknown';
    }
}

// Modal close function
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');

        // Clear form data when closing modals
        if (modalId === 'editUserModal') {
            document.getElementById('editUserForm').reset();
            currentEditUserId = null;
        } else if (modalId === 'bulkApprovalModal') {
            document.getElementById('bulkApprovalForm').reset();
        } else if (modalId === 'approvalModal') {
            document.getElementById('approvalForm').reset();
        }
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeData();

    // Add click event listeners for modal backgrounds
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal(modal.id);
            }
        });
    });
});
